import io
import tempfile
from concurrent.futures import Thread<PERSON>oolExecutor
from uuid import uuid4

import fitz  # PyMuPDF
import pytesseract
from fastapi import UploadFile
from fastapi.concurrency import run_in_threadpool
from langchain.schema import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from PIL import Image

from .....services.faiss import central_service
from .....utils.log import logger
import os
UPLOAD_DIR = os.path.join(os.getcwd(), "uploads")
os.makedirs(UPLOAD_DIR, exist_ok=True)

class EmbedController:
    @staticmethod
    def extract_page_text(page, page_num, use_ocr=True):
        """Extract text from a single page (direct text if available, else OCR)."""
        text = page.get_text("text")
        if text.strip():
            return {"page": page_num, "text": text}

        if use_ocr:
            pix = page.get_pixmap(dpi=200)
            img = Image.open(io.BytesIO(pix.tobytes("png")))
            ocr_text = pytesseract.image_to_string(img, lang="hin+guj+eng")
            return {"page": page_num, "text": ocr_text}

        return {"page": page_num, "text": ""}

    @staticmethod
    def parse_pdf_fast(path, max_workers=8, use_ocr=True):
        """Fast PDF parsing with parallelization + multilingual OCR fallback."""
        doc = fitz.open(path)
        pages = list(doc)

        results = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [
                executor.submit(EmbedController.extract_page_text, page, i + 1, use_ocr)
                for i, page in enumerate(pages)
            ]
            for f in futures:
                results.append(f.result())
        return results

    @staticmethod
    async def embedd_doc(file: UploadFile, user_id: str = "guest"):
        # Save uploaded file
        with tempfile.NamedTemporaryFile(
            delete=False, dir=UPLOAD_DIR, suffix=".pdf"
        ) as tmp_file:
            tmp_file.write(await file.read())
            file_path = tmp_file.name

        def parse_and_embed():
            parsed_content = EmbedController.parse_pdf_fast(
                file_path, max_workers=8, use_ocr=True
            )
            logger.info(f"Extracted {len(parsed_content)} pages from PDF")

            book_id = str(uuid4())[:6]

            raw_texts = [
                {
                    "page_content": c["text"],
                    "metadata": {"book": file.filename, "page": c["page"]},
                }
                for c in parsed_content
                if c["text"].strip()
            ]

            user_docs = [
                Document(page_content=c["page_content"], metadata=c["metadata"])
                for c in raw_texts
            ]

            # Chunking
            splitter = RecursiveCharacterTextSplitter(
                chunk_size=1000,
                chunk_overlap=200,
                separators=["\n\n", "\n", "।", ".", " "],
            )
            split_texts = splitter.split_documents(user_docs)

            central_service.add_documents(f"user_{user_id}_docs", split_texts)
            logger.info(
                f"Persisted {len(user_docs)} docs to central FAISS for user_id={user_id}"
            )
            return {"book_id": book_id, "pages_indexed": len(user_docs)}

        result = await run_in_threadpool(parse_and_embed)
        return {"status": "success", "user_id": user_id, **result}
